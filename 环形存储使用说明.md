# 环形存储使用说明

## 概述

环形存储是一种特殊的数据存储结构，它在Flash存储器上实现了先进先出(FIFO)的数据管理机制。当存储空间用完时，新数据会覆盖最旧的数据，形成一个循环。这种结构特别适合日志记录、数据采集和需要FIFO处理的应用场景。

本文档提供了环形存储在正式代码中的使用方法和注意事项。

## 环形存储在正式代码中的使用方法

### 初始化和清空（测试阶段）

```c
// 初始化SPI Flash和环形缓冲区
if (SPI_FLASH_Init() == HAL_OK) {
    printf("SPI Flash 初始化成功\r\n");
    
    // 如果需要清空环形缓冲区（谨慎使用，会删除所有数据）
    // SPI_FLASH_ClearRingBuffer();
}
```
对于需要在休眠/唤醒循环中保存数据的应用，建议：
void SystemInit(void) {
    // 其他初始化代码...
    
    // 只初始化SPI Flash，不清空环形缓冲区
    if (SPI_FLASH_Init() == HAL_OK) {
        printf("SPI Flash 初始化成功\r\n");
        printf("环形缓冲区状态 - 总记录数: %d, 未读记录数: %d\r\n", 
               SPI_FLASH_GetTotalRecordCount(), SPI_FLASH_GetRecordCount());
    }
    
    // 只有在特定条件下才清空环形缓冲区，例如：
    // - 首次使用设备
    // - 用户明确要求清空数据
    // - 检测到环形缓冲区数据损坏
    
    // 例如，可以通过按键组合或特定命令触发清空：
    // if (特定条件) {
    //     SPI_FLASH_ClearRingBuffer();
    //     printf("环形缓冲区已清空\r\n");
    // }
}

### 写入数据

```c
// 写入数据示例
HAL_StatusTypeDef WriteDataToRingBuffer(const char *data_str) {
    uint8_t writeData[SPI_FLASH_RECORD_SIZE];
    memset(writeData, 0, SPI_FLASH_RECORD_SIZE);
    
    // 确保数据不超过记录大小限制
    size_t data_len = strlen(data_str);
    if (data_len >= SPI_FLASH_RECORD_SIZE) {
        printf("数据过长，超过记录大小限制\r\n");
        return HAL_ERROR;
    }
    
    // 复制数据
    strcpy((char *)writeData, data_str);
    
    // 写入记录
    return SPI_FLASH_WriteRecord(writeData, data_len + 1); // +1 包含字符串结束符
}
```

### 读取数据

```c
// 读取数据示例
HAL_StatusTypeDef ReadDataFromRingBuffer(uint8_t *buffer, uint16_t *size) {
    // 检查是否有未读记录
    uint32_t unreadRecords = SPI_FLASH_GetRecordCount();
    if (unreadRecords == 0) {
        printf("没有未读记录\r\n");
        return HAL_ERROR;
    }
    
    // 读取最旧的未读记录（索引0）
    return SPI_FLASH_ReadRecord(0, buffer, size);
}
```

### 获取缓冲区状态

```c
// 获取环形缓冲区状态
void PrintRingBufferStatus(void) {
    printf("环形缓冲区状态:\r\n");
    printf("- 总记录数: %d\r\n", SPI_FLASH_GetTotalRecordCount());
    printf("- 未读记录数: %d\r\n", SPI_FLASH_GetRecordCount());
    printf("- 已读记录数: %d\r\n", RingBuffer.ReadRecords);
    printf("- 当前写入地址: 0x%08X\r\n", RingBuffer.CurrentAddr);
}
```

## 使用环形存储时的注意事项

### 1. 数据大小限制

- 每条记录的大小不能超过`SPI_FLASH_RECORD_SIZE`（在当前代码中是120字节）
- 如果需要存储更大的数据，可以修改`SPI_FLASH_RECORD_SIZE`的定义，但要注意这会影响环形缓冲区的总容量

```c
// 确保数据不超过记录大小限制
if (strlen(data_str) >= SPI_FLASH_RECORD_SIZE) {
    // 处理数据过长的情况
}
```

### 2. 读取顺序

- 环形缓冲区采用FIFO（先进先出）原则，始终应该从索引0开始读取（最旧的未读记录）
- 不能跳过记录读取，必须按顺序读取
- 每次读取后，该记录会被标记为已读，未读记录数会减少

### 3. 存储容量管理

- 环形缓冲区的总容量是固定的（`SPI_FLASH_RING_BUFFER_SIZE`，在当前代码中是4MB）
- 当缓冲区写满后，新数据会覆盖最旧的数据，即使这些数据尚未被读取
- 定期检查未读记录数，确保重要数据被及时读取

```c
// 检查未读记录数，如果接近容量上限，应该及时读取
uint32_t unreadRecords = SPI_FLASH_GetRecordCount();
uint32_t totalCapacity = SPI_FLASH_MAX_RECORDS;
if (unreadRecords > totalCapacity * 0.8) { // 如果已使用80%的容量
    printf("警告：未读记录数量较多，请及时读取\r\n");
}
```

### 4. 掉电保护

- 环形缓冲区的状态信息（当前写入地址、总记录数、已读记录数）存储在内部Flash中
- 这确保了掉电后数据不会丢失，重新上电后可以继续使用
- 但写入操作可能会被中断，导致最后一条数据不完整，应该有机制检测和处理这种情况

### 5. 擦写寿命

- Flash存储器有擦写寿命限制（通常为10万-100万次）
- 环形缓冲区会自动管理擦除操作，但频繁写入仍会加速Flash老化
- 考虑在非关键数据上减少写入频率，或实现数据缓存机制

### 6. 错误处理

- 所有SPI_FLASH函数都返回HAL_StatusTypeDef，应该检查返回值并处理可能的错误
- 常见错误包括：Flash忙、写入保护、地址错误等

```c
HAL_StatusTypeDef status = SPI_FLASH_WriteRecord(data, size);
if (status != HAL_OK) {
    // 根据错误类型进行处理
    switch (status) {
        case HAL_BUSY:
            printf("Flash忙，请稍后重试\r\n");
            break;
        case HAL_ERROR:
            printf("Flash操作错误\r\n");
            break;
        default:
            printf("未知错误: %d\r\n", status);
            break;
    }
}
```

### 7. 多线程/中断安全

- 如果在多线程或中断环境中使用，需要添加互斥保护机制
- 避免在写入或读取过程中被中断，可能导致数据不一致

```c
// 示例：使用简单的标志位保护（实际项目中可能需要更复杂的互斥机制）
static volatile uint8_t flash_busy = 0;

HAL_StatusTypeDef SafeWriteToRingBuffer(uint8_t *data, uint16_t size) {
    HAL_StatusTypeDef result;
    
    // 检查并设置忙标志
    if (flash_busy) {
        return HAL_BUSY;
    }
    flash_busy = 1;
    
    // 执行写入操作
    result = SPI_FLASH_WriteRecord(data, size);
    
    // 清除忙标志
    flash_busy = 0;
    
    return result;
}
```

### 8. 数据验证

- 考虑在数据中添加校验和或CRC，以便在读取时验证数据完整性
- 这对于检测Flash错误或掉电导致的数据损坏非常有用

```c
// 写入时添加简单校验和
uint8_t CalculateChecksum(const uint8_t *data, uint16_t size) {
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < size; i++) {
        checksum += data[i];
    }
    return checksum;
}

// 在数据末尾添加校验和后写入
void WriteDataWithChecksum(const char *data_str) {
    uint8_t buffer[SPI_FLASH_RECORD_SIZE];
    uint16_t data_len = strlen(data_str);
    
    memset(buffer, 0, SPI_FLASH_RECORD_SIZE);
    strcpy((char *)buffer, data_str);
    
    // 计算并添加校验和
    uint8_t checksum = CalculateChecksum(buffer, data_len);
    buffer[data_len] = checksum;
    
    SPI_FLASH_WriteRecord(buffer, data_len + 1);
}
```

## 环形存储API函数说明

### SPI_FLASH_Init

初始化SPI Flash和环形缓冲区。

```c
HAL_StatusTypeDef SPI_FLASH_Init(void);
```

### SPI_FLASH_WriteRecord

写入一条记录到环形缓冲区。

```c
HAL_StatusTypeDef SPI_FLASH_WriteRecord(uint8_t *pData, uint16_t Size);
```

- `pData`: 数据缓冲区指针
- `Size`: 数据大小（必须小于等于SPI_FLASH_RECORD_SIZE）

### SPI_FLASH_ReadRecord

读取环形缓冲区中的一条记录，并标记为已读。

```c
HAL_StatusTypeDef SPI_FLASH_ReadRecord(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size);
```

- `RecordIndex`: 记录索引（0表示最旧的未读记录，1表示次旧的未读记录，以此类推）
- `pData`: 数据缓冲区指针
- `Size`: 数据大小指针，输入表示缓冲区大小，输出表示实际读取大小

### SPI_FLASH_GetRecordCount

获取环形缓冲区中的未读记录数量。

```c
uint32_t SPI_FLASH_GetRecordCount(void);
```

### SPI_FLASH_GetTotalRecordCount

获取环形缓冲区中的总记录数量。

```c
uint32_t SPI_FLASH_GetTotalRecordCount(void);
```

### SPI_FLASH_ClearRingBuffer

清空环形缓冲区。

```c
HAL_StatusTypeDef SPI_FLASH_ClearRingBuffer(void);
```

### SPI_FLASH_MarkAllAsRead

标记所有记录为已读。

```c
HAL_StatusTypeDef SPI_FLASH_MarkAllAsRead(void);
```

## 结论

环形存储是一种高效的数据管理机制，特别适合需要持久化存储的嵌入式系统。通过正确使用本文档中的方法和遵循注意事项，可以在实际应用中安全、高效地使用环形存储功能。
