/**
  ******************************************************************************
  * @file    bsp_spi_flash.h
  * @brief   SPI Flash driver header file
  ******************************************************************************
  */

#ifndef __BSP_SPI_FLASH_H
#define __BSP_SPI_FLASH_H

#ifdef __cplusplus
 extern "C" {
#endif

#include "main.h"

// SPI Flash ID structure
typedef struct {
  uint8_t Manufacturer;
  uint8_t MemoryType;
  uint8_t Capacity;
} SPI_FLASH_ID_t;

// Ring buffer information structure
typedef struct {
  uint32_t CurrentAddr;
  uint32_t TotalRecords;
  uint32_t ReadRecords;
} SPI_FLASH_RingBuffer_t;

extern SPI_FLASH_RingBuffer_t RingBuffer;

// SPI Flash commands
#define SPI_FLASH_CMD_WRITE_ENABLE       0x06
#define SPI_FLASH_CMD_WRITE_DISABLE      0x04
#define SPI_FLASH_CMD_READ_ID            0x9F
#define SPI_FLASH_CMD_READ_STATUS        0x05
#define SPI_FLASH_CMD_WRITE_STATUS       0x01
#define SPI_FLASH_CMD_READ_DATA          0x03
#define SPI_FLASH_CMD_FAST_READ          0x0B
#define SPI_FLASH_CMD_PAGE_PROGRAM       0x02
#define SPI_FLASH_CMD_SECTOR_ERASE       0x20
#define SPI_FLASH_CMD_BLOCK_ERASE_32K    0x52
#define SPI_FLASH_CMD_BLOCK_ERASE_64K    0xD8
#define SPI_FLASH_CMD_CHIP_ERASE         0xC7
#define SPI_FLASH_CMD_POWER_DOWN         0xB9
#define SPI_FLASH_CMD_RELEASE_POWER_DOWN 0xAB
#define SPI_FLASH_CMD_DEVICE_ID          0xAB

// SPI Flash status register bits
#define SPI_FLASH_STATUS_BUSY            0x01
#define SPI_FLASH_STATUS_WEL             0x02

// SPI Flash parameters
#define SPI_FLASH_PAGE_SIZE              256
#define SPI_FLASH_SECTOR_SIZE            4096
#define SPI_FLASH_BLOCK_SIZE_32K         32768
#define SPI_FLASH_BLOCK_SIZE_64K         65536
#define SPI_FLASH_CAPACITY               4096

// Ring buffer parameters
#define SPI_FLASH_RING_BUFFER_START      0x00000
#define SPI_FLASH_RING_BUFFER_SIZE       0x400000
#define SPI_FLASH_RECORD_SIZE            120
#define SPI_FLASH_RING_BUFFER_END        (SPI_FLASH_RING_BUFFER_START + SPI_FLASH_RING_BUFFER_SIZE - 1)
#define SPI_FLASH_MAX_RECORDS            (SPI_FLASH_RING_BUFFER_SIZE / SPI_FLASH_RECORD_SIZE)

// Internal Flash storage indexes for ring buffer info
#define FLASH_RING_BUFFER_ADDR_INDEX         15
#define FLASH_RING_BUFFER_COUNT_INDEX        14

// Initialize SPI Flash
HAL_StatusTypeDef SPI_FLASH_Init(void);

// Read SPI Flash ID
HAL_StatusTypeDef SPI_FLASH_ReadID(SPI_FLASH_ID_t *ID);

// Erase SPI Flash sector
HAL_StatusTypeDef SPI_FLASH_EraseSector(uint32_t SectorAddr);

// Erase SPI Flash block (32KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock32K(uint32_t BlockAddr);

// Erase SPI Flash block (64KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock64K(uint32_t BlockAddr);

// Erase entire SPI Flash
HAL_StatusTypeDef SPI_FLASH_EraseChip(void);

// Read SPI Flash data
HAL_StatusTypeDef SPI_FLASH_ReadData(uint32_t ReadAddr, uint8_t *pData, uint16_t Size);

// Write SPI Flash data
HAL_StatusTypeDef SPI_FLASH_WriteData(uint32_t WriteAddr, uint8_t *pData, uint16_t Size);

// Initialize ring buffer
HAL_StatusTypeDef SPI_FLASH_InitRingBuffer(void);

// Write record to ring buffer
HAL_StatusTypeDef SPI_FLASH_WriteRecord(uint8_t *pData, uint16_t Size);

// Read record from ring buffer
HAL_StatusTypeDef SPI_FLASH_ReadRecord(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size);

// Read record from ring buffer with optional mark as read
HAL_StatusTypeDef SPI_FLASH_ReadRecordEx(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size, uint8_t MarkAsRead);

// Get unread record count in ring buffer
uint32_t SPI_FLASH_GetRecordCount(void);

// Get total record count in ring buffer
uint32_t SPI_FLASH_GetTotalRecordCount(void);

// Clear ring buffer
HAL_StatusTypeDef SPI_FLASH_ClearRingBuffer(void);

// Mark all records as read
HAL_StatusTypeDef SPI_FLASH_MarkAllAsRead(void);

// Mark specific record as read
HAL_StatusTypeDef SPI_FLASH_MarkRecordAsRead(uint32_t RecordIndex);

// Reset ring buffer and erase all data
HAL_StatusTypeDef SPI_FLASH_ResetRingBuffer(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_SPI_FLASH_H */
