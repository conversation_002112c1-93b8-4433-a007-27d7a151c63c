// 声明所有全局变量
#ifndef GLOBALS_H
#define GLOBALS_H
#include "main.h"
#include "rtc.h"

extern 	char str[20];
extern	uint8_t rx1_tmp[10];
extern	uint8_t tx1_tmp[10];
extern	uint8_t rx2_tmp[10];
extern	uint8_t tx2_tmp[10];
extern  uint8_t rx3_tmp[10];
extern	uint8_t tx3_tmp[10];
extern	uint8_t data;
extern	uint32_t ADC_Value[60];  // 3通道 × 20采样 = 60个数据
extern  int ad1,ad2,ad3,ad4,ad5,tp,td,button;
extern  float pw;
extern  float mcu_temp;  // MCU内部温度

extern float pitch ;  // 俯仰角
extern float roll ;    // 横滚角
extern float yaw ;      // 方位角

// 工作时间段结构体
typedef struct {
    uint8_t startHour;
    uint8_t startMinute;
    uint8_t endHour;
    uint8_t endMinute;
    uint8_t enabled;  // 0: 禁用, 1: 启用
} WorkTimeSlot;

// 全局变量
extern unsigned int sleepSeconds; // 休眠时间

#endif // GLOBALS_H
