/**
  ******************************************************************************
  * @file    GM20.h
  * <AUTHOR> Name
  * @brief   GM20 module communication interface driver
  ******************************************************************************
  */

#ifndef __GM20_H__
#define __GM20_H__

#include "main.h"
#include <stdint.h>
#include <string.h>
#include <stdio.h>

#define GM20_BUFFER_SIZE          256
#define GM20_RESPONSE_TIMEOUT     2000

typedef enum {
    GM20_OK = 0,
    GM20_ERROR,
    GM20_TIMEOUT
} GM20_StatusTypeDef;

// Basic commands
GM20_StatusTypeDef GM20_Init(void);
GM20_StatusTypeDef GM20_InitWithSleepTime(uint16_t sleep_timeout_seconds);
GM20_StatusTypeDef GM20_Wake(void);
GM20_StatusTypeDef GM20_SetEcho(uint8_t state);

// Information query commands
GM20_StatusTypeDef GM20_GetManufacturer(char *manufacturer);
GM20_StatusTypeDef GM20_GetModel(char *model);
GM20_StatusTypeDef GM20_GetVersion(char *version);
GM20_StatusTypeDef GM20_GetSN(char *sn);
GM20_StatusTypeDef GM20_GetCachedSN(char *sn);
GM20_StatusTypeDef GM20_GetDateTime(char *datetime);
GM20_StatusTypeDef GM20_SetDateTime(char *datetime);
GM20_StatusTypeDef GM20_GetBaudRate(char *baud);

// GPS related commands
GM20_StatusTypeDef GM20_StartGPS(void);
GM20_StatusTypeDef GM20_StopGPS(void);
GM20_StatusTypeDef GM20_GetLocation(char *location);
GM20_StatusTypeDef GM20_GetSignalQuality(int8_t *snr);

// Data transmission commands
GM20_StatusTypeDef GM20_SendHexData(uint8_t *data, uint16_t len, uint16_t *frameNo);
GM20_StatusTypeDef GM20_SendStringData(char *str, uint16_t len, uint16_t *frameNo);
GM20_StatusTypeDef GM20_SendFormatData(char *format_str, uint16_t *frameNo);

// Data query and deletion
GM20_StatusTypeDef GM20_QueryDataCount(uint16_t *count);
GM20_StatusTypeDef GM20_QueryAllData(void);
GM20_StatusTypeDef GM20_QueryData(uint16_t index);
GM20_StatusTypeDef GM20_ClearAllData(void);
GM20_StatusTypeDef GM20_ClearData(uint16_t index);
GM20_StatusTypeDef GM20_ClearStorageData(void);

// Configuration commands
GM20_StatusTypeDef GM20_SetStorageMode(uint8_t mode);
GM20_StatusTypeDef GM20_SetSleepMode(uint8_t mode, uint16_t level);
GM20_StatusTypeDef GM20_SetReceiveMode(uint8_t mode);
GM20_StatusTypeDef GM20_SetBaudRate(uint32_t baudrate);
GM20_StatusTypeDef GM20_ReadConfig(void);
GM20_StatusTypeDef GM20_ResetConfig(void);
GM20_StatusTypeDef GM20_Restart(void);

// Receive data commands
GM20_StatusTypeDef GM20_QueryReceiveCount(uint16_t *count);
GM20_StatusTypeDef GM20_ReadReceiveData(uint8_t *data, uint16_t *len, uint16_t *frameNo, uint32_t *time);

// Special format data transmission functions
GM20_StatusTypeDef GM20_SendSpecialFormat(float lat, float lon, float alt, float temp,
                                         uint8_t sat, float hdop, uint16_t power,
                                         int8_t rssi, float vbat, float v33,
                                         int8_t temp_mcu, uint8_t hum, uint8_t eco2,
                                         float tvoc, uint8_t pm25, uint16_t *frameNo);

// Simplified format data transmission function
GM20_StatusTypeDef GM20_SendSimpleFormat(const char *data_str, uint16_t *frameNo);

// UART operation functions
void GM20_SendCmd(char *cmd);
uint8_t GM20_WaitResponse(char *expected, uint32_t timeout);
void GM20_FlushBuffer(void);
void GM20_RxCallback(uint8_t data);

// External variable declarations
extern UART_HandleTypeDef hlpuart1;
extern char gm20RxBuffer[GM20_BUFFER_SIZE];

#endif /* __GM20_H__ */


