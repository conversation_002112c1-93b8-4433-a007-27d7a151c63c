/**
  ******************************************************************************
  * @file    bsp_spi_flash.c
  * @brief   SPI Flash driver source file
  ******************************************************************************
  */

#include "SPI_FLASH/bsp_spi_flash.h"
#include "FLASH/bsp_flash.h"
#include "spi.h"
#include <string.h>

#define SPI_FLASH_CS_LOW()    HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_RESET)
#define SPI_FLASH_CS_HIGH()   HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GP<PERSON>_PIN_SET)

SPI_FLASH_RingBuffer_t RingBuffer;
static uint8_t IsRingBufferInitialized = 0;

static HAL_StatusTypeDef SPI_FLASH_WaitForWriteEnd(void);
static HAL_StatusTypeDef SPI_FLASH_WriteEnable(void);
static HAL_StatusTypeDef SPI_FLASH_WritePage(uint32_t WriteAddr, uint8_t *pData, uint16_t Size);
static HAL_StatusTypeDef SPI_FLASH_SaveRingBufferInfo(void);
static HAL_StatusTypeDef SPI_FLASH_LoadRingBufferInfo(void);

// Wait for SPI Flash write completion
static HAL_StatusTypeDef SPI_FLASH_WaitForWriteEnd(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_READ_STATUS;
  uint8_t statusReg = 0;
  uint32_t timeout = 0xFFFFFF;

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  do
  {
    status = HAL_SPI_Receive(&hspi1, &statusReg, 1, 100);
    if (status != HAL_OK)
    {
      SPI_FLASH_CS_HIGH();
      return status;
    }

    timeout--;
  } while ((statusReg & SPI_FLASH_STATUS_BUSY) && (timeout > 0));

  SPI_FLASH_CS_HIGH();

  if (timeout == 0)
  {
    return HAL_TIMEOUT;
  }

  return HAL_OK;
}

// Enable SPI Flash write
static HAL_StatusTypeDef SPI_FLASH_WriteEnable(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_WRITE_ENABLE;

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);

  SPI_FLASH_CS_HIGH();

  return status;
}

// Write one page of data to SPI Flash
static HAL_StatusTypeDef SPI_FLASH_WritePage(uint32_t WriteAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  if (Size > SPI_FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_PAGE_PROGRAM;
  cmd[1] = (WriteAddr >> 16) & 0xFF;
  cmd[2] = (WriteAddr >> 8) & 0xFF;
  cmd[3] = WriteAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  status = HAL_SPI_Transmit(&hspi1, pData, Size, 1000);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Save ring buffer info to internal Flash
static HAL_StatusTypeDef SPI_FLASH_SaveRingBufferInfo(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = Flash_WriteUint32(FLASH_RING_BUFFER_ADDR_INDEX, RingBuffer.CurrentAddr);
  if (status != HAL_OK)
  {
    return status;
  }

  uint32_t recordInfo = ((RingBuffer.TotalRecords & 0xFFFF) << 16) | (RingBuffer.ReadRecords & 0xFFFF);
  status = Flash_WriteUint32(FLASH_RING_BUFFER_COUNT_INDEX, recordInfo);
  if (status != HAL_OK)
  {
    return status;
  }

  return HAL_OK;
}

// Load ring buffer info from internal Flash
static HAL_StatusTypeDef SPI_FLASH_LoadRingBufferInfo(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = Flash_ReadUint32(FLASH_RING_BUFFER_ADDR_INDEX, &RingBuffer.CurrentAddr);
  if (status != HAL_OK)
  {
    return status;
  }

  uint32_t recordInfo;
  status = Flash_ReadUint32(FLASH_RING_BUFFER_COUNT_INDEX, &recordInfo);
  if (status != HAL_OK)
  {
    return status;
  }

  RingBuffer.TotalRecords = (recordInfo >> 16) & 0xFFFF;
  RingBuffer.ReadRecords = recordInfo & 0xFFFF;

  return HAL_OK;
}

// Initialize SPI Flash
HAL_StatusTypeDef SPI_FLASH_Init(void)
{
  SPI_FLASH_CS_HIGH();

  HAL_Delay(100);

  return SPI_FLASH_InitRingBuffer();
}

// Read SPI Flash ID
HAL_StatusTypeDef SPI_FLASH_ReadID(SPI_FLASH_ID_t *ID)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_READ_ID;
  uint8_t id[3];

  if (ID == NULL)
  {
    return HAL_ERROR;
  }

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    return status;
  }

  status = HAL_SPI_Receive(&hspi1, id, 3, 100);

  SPI_FLASH_CS_HIGH();

  if (status == HAL_OK)
  {
    ID->Manufacturer = id[0];
    ID->MemoryType = id[1];
    ID->Capacity = id[2];
  }

  return status;
}

// Erase SPI Flash sector
HAL_StatusTypeDef SPI_FLASH_EraseSector(uint32_t SectorAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_SECTOR_ERASE;
  cmd[1] = (SectorAddr >> 16) & 0xFF;
  cmd[2] = (SectorAddr >> 8) & 0xFF;
  cmd[3] = SectorAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase SPI Flash block (32KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock32K(uint32_t BlockAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_BLOCK_ERASE_32K;
  cmd[1] = (BlockAddr >> 16) & 0xFF;
  cmd[2] = (BlockAddr >> 8) & 0xFF;
  cmd[3] = BlockAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase SPI Flash block (64KB)
HAL_StatusTypeDef SPI_FLASH_EraseBlock64K(uint32_t BlockAddr)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_BLOCK_ERASE_64K;
  cmd[1] = (BlockAddr >> 16) & 0xFF;
  cmd[2] = (BlockAddr >> 8) & 0xFF;
  cmd[3] = BlockAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Erase entire SPI Flash
HAL_StatusTypeDef SPI_FLASH_EraseChip(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd = SPI_FLASH_CMD_CHIP_ERASE;

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    return status;
  }

  status = SPI_FLASH_WriteEnable();
  if (status != HAL_OK)
  {
    return status;
  }

  SPI_FLASH_CS_LOW();

  status = HAL_SPI_Transmit(&hspi1, &cmd, 1, 100);

  SPI_FLASH_CS_HIGH();

  status = SPI_FLASH_WaitForWriteEnd();

  return status;
}

// Read SPI Flash data
HAL_StatusTypeDef SPI_FLASH_ReadData(uint32_t ReadAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t cmd[4];
  uint32_t primask;

  if (pData == NULL || Size == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  status = SPI_FLASH_WaitForWriteEnd();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  SPI_FLASH_CS_LOW();

  cmd[0] = SPI_FLASH_CMD_READ_DATA;
  cmd[1] = (ReadAddr >> 16) & 0xFF;
  cmd[2] = (ReadAddr >> 8) & 0xFF;
  cmd[3] = ReadAddr & 0xFF;

  status = HAL_SPI_Transmit(&hspi1, cmd, 4, 100);
  if (status != HAL_OK)
  {
    SPI_FLASH_CS_HIGH();
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  status = HAL_SPI_Receive(&hspi1, pData, Size, 1000);

  SPI_FLASH_CS_HIGH();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Write SPI Flash data
HAL_StatusTypeDef SPI_FLASH_WriteData(uint32_t WriteAddr, uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint16_t pageRemain;
  uint16_t currentSize;
  uint32_t currentAddr;
  uint16_t currentOffset;
  uint32_t primask;

  if (pData == NULL || Size == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  currentAddr = WriteAddr;
  currentOffset = 0;

  while (currentOffset < Size)
  {
    pageRemain = SPI_FLASH_PAGE_SIZE - (currentAddr % SPI_FLASH_PAGE_SIZE);

    currentSize = (currentOffset + pageRemain <= Size) ? pageRemain : (Size - currentOffset);

    status = SPI_FLASH_WritePage(currentAddr, &pData[currentOffset], currentSize);
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }

    currentAddr += currentSize;
    currentOffset += currentSize;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Initialize ring buffer
HAL_StatusTypeDef SPI_FLASH_InitRingBuffer(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = SPI_FLASH_LoadRingBufferInfo();

  if (status != HAL_OK ||
      RingBuffer.CurrentAddr > SPI_FLASH_RING_BUFFER_END)
  {
    RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START;
    RingBuffer.TotalRecords = 0;
    RingBuffer.ReadRecords = 0;

    status = SPI_FLASH_SaveRingBufferInfo();
    if (status != HAL_OK)
    {
      return status;
    }

    status = SPI_FLASH_EraseBlock64K(SPI_FLASH_RING_BUFFER_START);
    if (status != HAL_OK)
    {
      return status;
    }
  }

  IsRingBufferInitialized = 1;

  return HAL_OK;
}

// Write record to ring buffer
HAL_StatusTypeDef SPI_FLASH_WriteRecord(uint8_t *pData, uint16_t Size)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint8_t recordBuffer[SPI_FLASH_RECORD_SIZE];
  uint32_t nextAddr;
  uint32_t sectorAddr;
  uint32_t primask;

  if (pData == NULL || Size == 0 || Size > SPI_FLASH_RECORD_SIZE)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!IsRingBufferInitialized)
  {
    status = SPI_FLASH_InitRingBuffer();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  memset(recordBuffer, 0xFF, SPI_FLASH_RECORD_SIZE);
  memcpy(recordBuffer, pData, Size);

  nextAddr = RingBuffer.CurrentAddr + SPI_FLASH_RECORD_SIZE;
  if (nextAddr > SPI_FLASH_RING_BUFFER_END)
  {
    nextAddr = SPI_FLASH_RING_BUFFER_START;
  }

  if (nextAddr % SPI_FLASH_SECTOR_SIZE == 0 ||
      nextAddr == SPI_FLASH_RING_BUFFER_START)
  {
    sectorAddr = nextAddr;
    status = SPI_FLASH_EraseSector(sectorAddr);
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  status = SPI_FLASH_WriteData(RingBuffer.CurrentAddr, recordBuffer, SPI_FLASH_RECORD_SIZE);
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  RingBuffer.CurrentAddr = nextAddr;
  RingBuffer.TotalRecords++;

  if (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) {
    RingBuffer.ReadRecords++;
  }

  status = SPI_FLASH_SaveRingBufferInfo();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read record from ring buffer with optional mark as read
HAL_StatusTypeDef SPI_FLASH_ReadRecordEx(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size, uint8_t MarkAsRead)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t readAddr;
  uint32_t primask;

  if (pData == NULL || Size == NULL || *Size == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!IsRingBufferInitialized)
  {
    status = SPI_FLASH_InitRingBuffer();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  uint32_t unreadRecords = SPI_FLASH_GetRecordCount();

  if (unreadRecords == 0)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  if (RecordIndex >= unreadRecords || (RecordIndex > 0 && MarkAsRead))
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  uint32_t physicalIndex;

  physicalIndex = (RingBuffer.ReadRecords + RecordIndex) % SPI_FLASH_MAX_RECORDS;

  readAddr = SPI_FLASH_RING_BUFFER_START + (physicalIndex * SPI_FLASH_RECORD_SIZE);

  if (readAddr >= SPI_FLASH_RING_BUFFER_END) {
    readAddr = SPI_FLASH_RING_BUFFER_START + (readAddr - SPI_FLASH_RING_BUFFER_END);
  }

  status = SPI_FLASH_ReadData(readAddr, pData, SPI_FLASH_RECORD_SIZE);
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  *Size = SPI_FLASH_RECORD_SIZE;

  if (MarkAsRead) {
    if (RecordIndex == 0 && RingBuffer.ReadRecords < RingBuffer.TotalRecords) {
      RingBuffer.ReadRecords++;
      status = SPI_FLASH_SaveRingBufferInfo();
      if (status != HAL_OK) {
        if (!primask)
        {
          __enable_irq();
        }
        return status;
      }
    }
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Read record from ring buffer (backward compatible version)
HAL_StatusTypeDef SPI_FLASH_ReadRecord(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size)
{
  return SPI_FLASH_ReadRecordEx(RecordIndex, pData, Size, 1);
}

// Get unread record count in ring buffer
uint32_t SPI_FLASH_GetRecordCount(void)
{
  if (!IsRingBufferInitialized)
  {
    if (SPI_FLASH_InitRingBuffer() != HAL_OK)
    {
      return 0;
    }
  }

  uint32_t unreadRecords = 0;

  if (RingBuffer.TotalRecords >= RingBuffer.ReadRecords)
  {
    unreadRecords = RingBuffer.TotalRecords - RingBuffer.ReadRecords;
  }

  if (unreadRecords > SPI_FLASH_MAX_RECORDS)
  {
    unreadRecords = SPI_FLASH_MAX_RECORDS;
  }

  return unreadRecords;
}

// Get total record count in ring buffer
uint32_t SPI_FLASH_GetTotalRecordCount(void)
{
  if (!IsRingBufferInitialized)
  {
    if (SPI_FLASH_InitRingBuffer() != HAL_OK)
    {
      return 0;
    }
  }

  uint32_t totalRecords = RingBuffer.TotalRecords;

  if (totalRecords > SPI_FLASH_MAX_RECORDS)
  {
    totalRecords = SPI_FLASH_MAX_RECORDS;
  }

  return totalRecords;
}

// Clear ring buffer
HAL_StatusTypeDef SPI_FLASH_ClearRingBuffer(void)
{
  HAL_StatusTypeDef status = HAL_OK;

  status = SPI_FLASH_EraseBlock64K(SPI_FLASH_RING_BUFFER_START);
  if (status != HAL_OK)
  {
    return status;
  }

  RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START;
  RingBuffer.TotalRecords = 0;
  RingBuffer.ReadRecords = 0;

  status = SPI_FLASH_SaveRingBufferInfo();

  return status;
}

// Mark all records as read
HAL_StatusTypeDef SPI_FLASH_MarkAllAsRead(void)
{
  if (!IsRingBufferInitialized)
  {
    if (SPI_FLASH_InitRingBuffer() != HAL_OK)
    {
      return HAL_ERROR;
    }
  }

  RingBuffer.ReadRecords = RingBuffer.TotalRecords;

  return SPI_FLASH_SaveRingBufferInfo();
}

// Mark specific record as read
HAL_StatusTypeDef SPI_FLASH_MarkRecordAsRead(uint32_t RecordIndex)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t recordCount;
  uint32_t unreadRecords;
  uint32_t primask;

  primask = __get_PRIMASK();
  __disable_irq();

  if (!IsRingBufferInitialized)
  {
    status = SPI_FLASH_InitRingBuffer();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  recordCount = SPI_FLASH_GetTotalRecordCount();
  unreadRecords = SPI_FLASH_GetRecordCount();

  if (RecordIndex >= recordCount)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  if (RecordIndex == 0 && unreadRecords > 0)
  {
    RingBuffer.ReadRecords++;
    if (RingBuffer.ReadRecords > RingBuffer.TotalRecords)
    {
      RingBuffer.ReadRecords = RingBuffer.TotalRecords;
    }
    status = SPI_FLASH_SaveRingBufferInfo();
  }
  else if (RecordIndex > 0 && RecordIndex < unreadRecords)
  {
    status = HAL_ERROR;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}
