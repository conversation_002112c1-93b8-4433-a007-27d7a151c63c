/**
  * @file    GM20.c
  * @brief   GM20 module communication interface driver
  */

#include "GM20.h"
#include <stdlib.h>  // Add stdlib.h header for atoi function support

char gm20RxBuffer[GM20_BUFFER_SIZE];  // Remove static, allow external access
static uint16_t gm20RxIndex = 0;
static volatile uint8_t gm20ResponseReady = 0;  // GM20 response ready flag
extern UART_HandleTypeDef hlpuart1;
static void GM20_Delay(uint32_t ms);

// Global variable to store device serial number, read once during initialization
static char gm20_device_sn[32] = {0};
static uint8_t gm20_sn_initialized = 0;

// Initialize GM20 module with sleep timeout
GM20_StatusTypeDef GM20_InitWithSleepTime(uint16_t sleep_timeout_seconds)
{
    GM20_StatusTypeDef status;

    // Wait for module power stabilization
    HAL_Delay(3000);

    // Clear receive buffer
    GM20_FlushBuffer();

    // Wake up module
    status = GM20_Wake();
    if (status != GM20_OK) {
        return status;
    }

    // Turn off echo
    status = GM20_SetEcho(0);
    if (status != GM20_OK) {
        return status;
    }

    // Set sleep mode
    if (sleep_timeout_seconds == 0) {
        // Set to no sleep mode
//        printf("Setting GM20 to no sleep mode\r\n");
        GM20_SendCmd("AT+CPSM=0\r\n");
        if (!GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
            printf("GM20 no sleep mode setting failed\r\n");
            // Sleep mode setting failure doesn't affect other functions, continue execution
        } else {
            printf("GM20 no sleep mode set successfully\r\n");
        }
    } else if (sleep_timeout_seconds == 1) {
        // Set to AT+CPSM=1,9 mode
        GM20_SendCmd("AT+CPSM=1,9\r\n");
        if (!GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
            printf("GM20 sleep mode err\r\n");
            // Sleep mode setting failure doesn't affect other functions, continue execution
        } else {
            printf("Setting GM20 to sleep mode: AT+CPSM=1,9\r\n");
        }
    }

    // Read device serial number (only once during initialization)
//    printf("Reading GM20 device serial number...\r\n");
    status = GM20_GetSN(gm20_device_sn);
    if (status == GM20_OK) {
        // SN read successful, but don't output here (avoid duplicate output)
        gm20_sn_initialized = 1;
    } else {
//        printf("GM20 SN read failed, using default\r\n");
        // If read fails, use default value
        strcpy(gm20_device_sn, "00000000");
        gm20_sn_initialized = 1;
    }

    return GM20_OK;
}

// Initialize GM20 module (compatibility function, uses default 10 second sleep)
GM20_StatusTypeDef GM20_Init(void)
{
    return GM20_InitWithSleepTime(10); // Default 10 second sleep
}

// Wake up GM20 module
GM20_StatusTypeDef GM20_Wake(void)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    // Wait for module stabilization
    HAL_Delay(500);  // Reduced delay: 1000ms -> 500ms

    // Multiple attempts to send AT command to wake up module
    for (int i = 0; i < 8; i++) {
        // Clear receive buffer
        GM20_FlushBuffer();

        // Send AT command (module needs multiple sends to wake up from sleep)
        GM20_SendCmd("AT\r\n");

        // Brief wait, then send again (special handling for sleeping module)
        HAL_Delay(100);  // Reduced delay: 200ms -> 100ms
        GM20_SendCmd("AT\r\n");

        // Wait for module response
        if (GM20_WaitResponse("OK", 1000)) {  // Reduced timeout: 1500ms -> 1000ms
            // Send AT once more to confirm module is fully awake
            HAL_Delay(100);  // Reduced delay: 200ms -> 100ms
            GM20_FlushBuffer();
            GM20_SendCmd("AT\r\n");
            if (GM20_WaitResponse("OK", 800)) {  // Reduced timeout: 1000ms -> 800ms
                return GM20_OK;
            }
        }

        // If failed, wait before trying again
        HAL_Delay(300);  // Reduced delay: 500ms -> 300ms
    }

    return GM20_ERROR;
}

// Set echo mode
GM20_StatusTypeDef GM20_SetEcho(uint8_t state)
{
    char cmd[8];
    sprintf(cmd, "ATE%d\r\n", state);
    GM20_SendCmd(cmd);
    if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
        return GM20_OK;
    }
    return GM20_ERROR;
}

// Get current baud rate
GM20_StatusTypeDef GM20_GetBaudRate(char *baud)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    // Send query baud rate command
    GM20_SendCmd("AT+CGBR?\r\n");

    // Wait for response
    if (GM20_WaitResponse("+CGBR:", GM20_RESPONSE_TIMEOUT)) {
        // Find baud rate after +CGBR:
        char *p = strstr(gm20RxBuffer, "+CGBR:");
        if (p != NULL) {
            p += 6; // Skip "+CGBR:"

            // Skip possible spaces
            while (*p == ' ' || *p == '\t' || *p == '\r' || *p == '\n') {
                p++;
            }

            // Copy baud rate to output buffer
            int i = 0;
            while (p[i] && p[i] != '\r' && p[i] != '\n' && i < 15) {
                baud[i] = p[i];
                i++;
            }
            baud[i] = '\0';

            // Wait for OK response
            if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                return GM20_OK;
            }
        }
    }

    // If no correct response received, set default value
    strcpy(baud, "UNKNOWN");
    return GM20_ERROR;
}

// Get product serial number
GM20_StatusTypeDef GM20_GetSN(char *sn)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    // Send query serial number command
    GM20_SendCmd("AT+CGSN?\r\n");

    // Wait for response
    if (GM20_WaitResponse("+CGSN:", GM20_RESPONSE_TIMEOUT)) {
        // Find serial number after +CGSN:
        char *p = strstr(gm20RxBuffer, "+CGSN:");
        if (p != NULL) {
            p += 6; // Skip "+CGSN:"

            // Skip possible spaces
            while (*p == ' ' || *p == '\t' || *p == '\r' || *p == '\n') {
                p++;
            }

            // Copy serial number to output buffer
            int i = 0;
            while (p[i] && p[i] != '\r' && p[i] != '\n' && i < 31) {
                sn[i] = p[i];
                i++;
            }
            sn[i] = '\0';

            // Wait for OK response
            if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                return GM20_OK;
            }
        }
    }

    // If no correct response received, set default value
    strcpy(sn, "UNKNOWN");
    return GM20_ERROR;
}

// Get cached device serial number (avoid repeated queries)
GM20_StatusTypeDef GM20_GetCachedSN(char *sn)
{
    if (gm20_sn_initialized) {
        strcpy(sn, gm20_device_sn);
        return GM20_OK;
    } else {
        // If not initialized, use default value
        strcpy(sn, "00000000");
        return GM20_ERROR;
    }
}




// Get satellite signal quality
GM20_StatusTypeDef GM20_GetSignalQuality(int8_t *snr)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    // Send query signal quality command
    GM20_SendCmd("AT+CESQ?\r\n");

    // Wait for response
    if (GM20_WaitResponse("+CESQ:", GM20_RESPONSE_TIMEOUT)) {
        // Find number after +CESQ:
        char *p = strstr(gm20RxBuffer, "+CESQ:");
        if (p != NULL) {
            p += 6; // Skip "+CESQ:"

            // Skip possible spaces
            while (*p == ' ' || *p == '\t' || *p == '\r' || *p == '\n') {
                p++;
            }

            // Parse signal quality value
            *snr = atoi(p);

            // Wait for OK response
            if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                return GM20_OK;
            }
        }
    }

    // If no correct response received, set default value
    *snr = -128; // Indicates no signal
    return GM20_ERROR;
}

// Send string data to storage area
GM20_StatusTypeDef GM20_SendStringData(char *str, uint16_t len, uint16_t *frameNo)
{
    if (len > 120) {
        return GM20_ERROR; // Exceeds maximum length limit
    }

    char cmd[300] = {0};

    // Clear receive buffer
    GM20_FlushBuffer();

    sprintf(cmd, "AT+STRING=%d,%s\r\n", len, str);
    GM20_SendCmd(cmd);

    // First wait for +FrameNo: response
    if (GM20_WaitResponse("+FrameNo:", GM20_RESPONSE_TIMEOUT)) {
        char *p = strstr(gm20RxBuffer, "+FrameNo:");
        if (p != NULL) {
            sscanf(p, "+FrameNo:%hu", frameNo);

            // Then wait for OK response
            if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                return GM20_OK;
            }
        }
    }
    return GM20_ERROR;
}



// Query pending data count in storage area
GM20_StatusTypeDef GM20_QueryDataCount(uint16_t *count)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    GM20_SendCmd("AT+CMMQ?\r\n");

    // First wait for +CMMQ: response
    if (GM20_WaitResponse("+CMMQ:", GM20_RESPONSE_TIMEOUT)) {
        char *p = strstr(gm20RxBuffer, "+CMMQ:");
        if (p != NULL) {
            sscanf(p, "+CMMQ:%hu", count);

            // Then wait for OK response
            if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                return GM20_OK;
            }
        }
    }
    return GM20_ERROR;
}


// Set sleep mode
GM20_StatusTypeDef GM20_SetSleepMode(uint8_t mode, uint16_t level)
{
    char cmd[20];
    if (mode == 0) {
        sprintf(cmd, "AT+CPSM=%d\r\n", mode);
    } else {
        // According to manual: use 240M format when level=240, otherwise use numeric level
        if (level == 240) {
            sprintf(cmd, "AT+CPSM=%d,240M\r\n", mode);
        } else {
            sprintf(cmd, "AT+CPSM=%d,%d\r\n", mode, level);
        }
    }

    GM20_SendCmd(cmd);
    if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
        return GM20_OK;
    }
    return GM20_ERROR;
}

// Clear all pending data in storage area
GM20_StatusTypeDef GM20_ClearStorageData(void)
{
    // Clear receive buffer
    GM20_FlushBuffer();

    // Send clear storage area command
    GM20_SendCmd("AT+CCLR\r\n");

    // Wait for +CCLR:OK response
    if (GM20_WaitResponse("+CCLR:OK", GM20_RESPONSE_TIMEOUT)) {
        // Then wait for final OK response
        if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
            return GM20_OK;
        }
    }

    return GM20_ERROR;
}


// Restart module
GM20_StatusTypeDef GM20_Restart(void)
{
    GM20_SendCmd("AT+CRST\r\n");
    if (GM20_WaitResponse("+CRST:OK", GM20_RESPONSE_TIMEOUT)) {
        return GM20_OK;
    }
    return GM20_ERROR;
}

// Query received data frame count
GM20_StatusTypeDef GM20_QueryReceiveCount(uint16_t *count)
{
    GM20_SendCmd("AT+READ?\r\n");
    if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
        char *p = strstr(gm20RxBuffer, "+READ:");
        if (p != NULL) {
            sscanf(p, "+READ:%hu", count);
            return GM20_OK;
        }
    }
    return GM20_ERROR;
}

// Read received data
GM20_StatusTypeDef GM20_ReadReceiveData(uint8_t *data, uint16_t *len, uint16_t *frameNo, uint32_t *time)
{
    GM20_SendCmd("AT+read\r\n");
    if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
        char *p = strstr(gm20RxBuffer, "+RECV:");
        if (p != NULL) {
            int length;
            char hex_data[256] = {0};

            sscanf(p, "+RECV:%d,%[^,],%hu,%u", &length, hex_data, frameNo, time);

            if (length > 0) {
                *len = length;

                // Convert hex string to byte data
                for (int i = 0; i < length; i++) {
                    sscanf(hex_data + i*2, "%2hhx", &data[i]);
                }

                return GM20_OK;
            } else {
                *len = 0;
                return GM20_OK;
            }
        }
    }
    return GM20_ERROR;
}

// Send AT command
void GM20_SendCmd(char *cmd)
{
    HAL_StatusTypeDef result = HAL_UART_Transmit(&hlpuart1, (uint8_t*)cmd, strlen(cmd), 100);
    if (result != HAL_OK) {
        printf("GM20: UART transmit failed, status=%d\r\n", result);
    }
}

// Wait for module response
uint8_t GM20_WaitResponse(char *expected, uint32_t timeout)
{
    uint32_t start = HAL_GetTick();

    if (strstr(gm20RxBuffer, expected) != NULL) {
        return 1;
    }

    uint8_t expecting_ok = (strcmp(expected, "OK") == 0);

    while ((HAL_GetTick() - start) < timeout) {
        if (gm20ResponseReady) {
            gm20ResponseReady = 0;

            if (strstr(gm20RxBuffer, expected) != NULL) {
                return 1;
            }

            if (expecting_ok && strstr(gm20RxBuffer, "\r\nOK\r\n") != NULL) {
                return 1;
            }

            if (strstr(gm20RxBuffer, "ERROR") != NULL) {
                return 0;
            }

            if (strstr(gm20RxBuffer, "+CME ERROR") != NULL) {
                return 0;
            }
        }

        GM20_Delay(10);
    }

    if (strstr(gm20RxBuffer, expected) != NULL) {
        return 1;
    }

    if (expecting_ok && strstr(gm20RxBuffer, "\r\nOK\r\n") != NULL) {
        return 1;
    }

    return 0;
}

// Clear receive buffer
void GM20_FlushBuffer(void)
{
    memset(gm20RxBuffer, 0, GM20_BUFFER_SIZE);
    gm20RxIndex = 0;
    gm20ResponseReady = 0;
}

// UART receive callback function
void GM20_RxCallback(uint8_t data)
{
    if (gm20RxIndex < GM20_BUFFER_SIZE - 1) {
        gm20RxBuffer[gm20RxIndex++] = data;
        gm20RxBuffer[gm20RxIndex] = '\0';

        static uint8_t cr_received = 0;
        if (data == '\r') {
            cr_received = 1;
        } else if (data == '\n' && cr_received) {
            if (strstr(gm20RxBuffer, "OK") != NULL) {
                gm20ResponseReady = 1;
            }
            else if (strstr(gm20RxBuffer, "ERROR") != NULL) {
                gm20ResponseReady = 1;
            }
            else if (strstr(gm20RxBuffer, "+CME ERROR") != NULL) {
                gm20ResponseReady = 1;
            }

            cr_received = 0;
        } else {
            cr_received = 0;
        }
    } else {
        GM20_FlushBuffer();
    }
}

// Delay function
static void GM20_Delay(uint32_t ms)
{
    HAL_Delay(ms);
}

// Send data in special format
GM20_StatusTypeDef GM20_SendSpecialFormat(float lat, float lon, float alt, float temp,
                                         uint8_t sat, float hdop, uint16_t power,
                                         int8_t rssi, float vbat, float v33,
                                         int8_t temp_mcu, uint8_t hum, uint8_t eco2,
                                         float tvoc, uint8_t pm25, uint16_t *frameNo)
{
    char data_str[150];

    snprintf(data_str, sizeof(data_str),
             "HX+%.5f+%.5f+%.6f+%.1f+%d+%.2f+%d+%d+%.1f+%.1f+%d+%d+%d+%.2f+%d+E",
             lat, lon, alt, temp, sat, hdop, power, rssi, vbat, v33,
             temp_mcu, hum, eco2, tvoc, pm25);

    uint16_t len = strlen(data_str);

    return GM20_SendStringData(data_str, len, frameNo);
}

// Send data in simplified format
GM20_StatusTypeDef GM20_SendSimpleFormat(const char *data_str, uint16_t *frameNo)
{
    uint16_t len = strlen(data_str);

    if (len < 5 || data_str[0] != 'H' || data_str[1] != 'X' || data_str[2] != '+' ||
        data_str[len-2] != '+' || data_str[len-1] != 'E') {
        return GM20_ERROR;
    }

    return GM20_SendStringData((char *)data_str, len, frameNo);
}




