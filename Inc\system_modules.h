/**
  ******************************************************************************
  * @file           : system_modules.h
  * @brief          : Header for modularized system functions
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

#ifndef __SYSTEM_MODULES_H
#define __SYSTEM_MODULES_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Exported types ------------------------------------------------------------*/

/* RTOS通信数据结构 */
typedef struct {
    GPS_Data_t gps_data;
    uint32_t timestamp;
    uint8_t valid;
} GPSQueueData_t;

typedef struct {
    LSM6DS3_Data imu_data;
    LSM6DS3_Attitude attitude_data;
    uint32_t timestamp;
    uint8_t valid;
} SensorQueueData_t;

typedef struct {
    char command[64];
    uint32_t timestamp;
    uint8_t valid;
} GM20CmdData_t;

/* 任务同步标志位定义（使用信号量实现） */
#define GPS_READY_BIT       (1UL << 0)
#define SENSOR_READY_BIT    (1UL << 1)
#define DATA_SENT_BIT       (1UL << 2)
#define SLEEP_READY_BIT     (1UL << 3)

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions prototypes ---------------------------------------------*/

/* ========== 电源管理模块 ========== */
/**
 * @brief 电源管理模块初始化
 * @retval HAL状态
 */
HAL_StatusTypeDef PowerModule_Init(void);

/**
 * @brief 读取电池电压（3次平均）
 * @retval HAL状态
 */
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void);

/**
 * @brief 启用外设电源
 */
void PowerModule_EnablePeripherals(void);

/**
 * @brief 禁用外设电源
 */
void PowerModule_DisablePeripherals(void);

/* ========== GPS模块 ========== */
/**
 * @brief GPS模块初始化
 * @retval HAL状态
 */
HAL_StatusTypeDef GPSModule_Init(void);

/**
 * @brief 等待GPS数据
 * @param timeout_ms: 超时时间（毫秒）
 * @param is_first_boot: 是否首次启动
 * @retval HAL状态
 */
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t timeout_ms, uint8_t is_first_boot);

/**
 * @brief GPS模块上电
 */
void GPSModule_PowerOn(void);

/**
 * @brief GPS模块断电
 */
void GPSModule_PowerOff(void);

/**
 * @brief GPS时钟同步
 * @retval HAL状态
 */
HAL_StatusTypeDef GPSModule_SyncRTC(void);

/* ========== 传感器模块 ========== */
/**
 * @brief 传感器模块初始化
 * @retval HAL状态
 */
HAL_StatusTypeDef SensorModule_Init(void);

/**
 * @brief 读取传感器数据
 * @retval HAL状态
 */
HAL_StatusTypeDef SensorModule_ReadData(void);

/* ========== GM20通信模块 ========== */
/**
 * @brief GM20模块初始化
 * @param is_first_boot: 是否首次启动
 * @retval HAL状态
 */
HAL_StatusTypeDef GM20Module_Init(uint8_t is_first_boot);

/**
 * @brief GM20模块唤醒
 * @retval HAL状态
 */
HAL_StatusTypeDef GM20Module_WakeUp(void);

/**
 * @brief GM20模块发送数据
 * @retval HAL状态
 */
HAL_StatusTypeDef GM20Module_SendData(void);

/**
 * @brief GM20模块上电
 */
void GM20Module_PowerOn(void);

/**
 * @brief GM20模块断电
 */
void GM20Module_PowerOff(void);

/* ========== 数据处理模块 ========== */
/**
 * @brief 创建数据包
 * @param output_buffer: 输出缓冲区
 * @param buffer_size: 缓冲区大小
 * @retval HAL状态
 */
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size);

/**
 * @brief 发送数据到GM20
 * @param data_string: 数据字符串
 * @retval HAL状态
 */
HAL_StatusTypeDef DataModule_SendToGM20(const char *data_string);

/* ========== 系统控制模块 ========== */
/**
 * @brief 打印当前时间
 */
void SystemModule_PrintCurrentTime(void);

/**
 * @brief 系统进入休眠模式
 */
void SystemModule_EnterSleepMode(void);

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_MODULES_H */
